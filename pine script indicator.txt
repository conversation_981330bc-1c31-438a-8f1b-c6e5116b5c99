//@version=6
indicator("Indian Intraday Scalping Signals", shorttitle="IISS", overlay=true, max_bars_back=500)

// ═══════════════════════════════════════════════════════════════════════════════
// INDIAN INTRADAY SCALPING INDICATOR - VISUAL SIGNALS COMPANION
// Mirrors the main strategy logic for manual trading and automation support
// Target: Nifty 50 & Sensex indices - Quick intraday opportunities
// Market: Indian stock exchanges (NSE/BSE) - Individual retail trader focused
// ═══════════════════════════════════════════════════════════════════════════════

// DISPLAY SETTINGS
show_signals = input.bool(true, "📊 Show Buy/Sell Signals", tooltip="Display buy/sell signal markers on chart")
show_ema_lines = input.bool(true, "📈 Show EMA Lines", tooltip="Display fast and slow EMA lines")
show_signal_labels = input.bool(true, "🏷️ Show Signal Labels", tooltip="Show text labels on signals")
show_strength_histogram = input.bool(false, "📊 Show Signal Strength", tooltip="Display signal strength as histogram")
signal_size = input.string("Normal", "📏 Signal Size", options=["Small", "Normal", "Large"], tooltip="Size of signal markers")

// TECHNICAL SETTINGS - IDENTICAL TO STRATEGY
ema_fast = input.int(5, "⚡ Fast EMA", minval=3, maxval=10, tooltip="Fast EMA for trend detection")
ema_slow = input.int(13, "🐌 Slow EMA", minval=8, maxval=21, tooltip="Slow EMA for trend confirmation")
rsi_period = input.int(7, "📊 RSI Period", minval=5, maxval=14, tooltip="RSI period for momentum")
rsi_overbought = input.int(75, "📈 RSI Overbought", minval=70, maxval=85, tooltip="RSI overbought level")
rsi_oversold = input.int(25, "📉 RSI Oversold", minval=15, maxval=30, tooltip="RSI oversold level")
volume_multiplier = input.float(1.5, "📊 Volume Spike", minval=1.2, maxval=3.0, step=0.1, tooltip="Volume spike multiplier")
atr_period = input.int(14, "📏 ATR Period", minval=10, maxval=20, tooltip="ATR period for volatility")

// SIGNAL FILTERS - IDENTICAL TO STRATEGY
min_candle_gap = input.int(2, "⏱️ Min Candle Gap", minval=1, maxval=5, tooltip="Minimum candles between signals")
volatility_filter = input.bool(true, "🌊 Volatility Filter", tooltip="Filter signals during high volatility")
volume_filter = input.bool(true, "📊 Volume Filter", tooltip="Require volume confirmation")

// MARKET SESSION SETTINGS
trading_session = input.session("0915-1530", "📅 Trading Session (IST)", tooltip="Indian market hours: 9:15 AM to 3:30 PM IST")
highlight_session = input.bool(true, "🕐 Highlight Market Hours", tooltip="Highlight Indian market trading session")

// ALERT SETTINGS
enable_buy_alerts = input.bool(false, "🔔 Enable Buy Alerts", tooltip="Send alerts on buy signals")
enable_sell_alerts = input.bool(false, "🔔 Enable Sell Alerts", tooltip="Send alerts on sell signals")
enable_hold_alerts = input.bool(false, "🔔 Enable Hold Alerts", tooltip="Send alerts when no clear signal")

// ═══════════════════════════════════════════════════════════════════════════════
// MARKET SESSION DETECTION
// ═══════════════════════════════════════════════════════════════════════════════

// Indian market session detection
in_session = time(timeframe.period, trading_session, "Asia/Kolkata")
is_market_open = not na(in_session)

// Market opening detection (first 15 minutes)
current_time_ist = time("1", "0000-2359", "Asia/Kolkata")
market_open_time = timestamp("Asia/Kolkata", year, month, dayofmonth, 9, 15)
is_market_opening = current_time_ist >= market_open_time and current_time_ist <= market_open_time + 15 * 60 * 1000

// Square-off time (3:20 PM IST)
square_off_timestamp = timestamp("Asia/Kolkata", year, month, dayofmonth, 15, 20)
is_square_off_time = current_time_ist >= square_off_timestamp

// ═══════════════════════════════════════════════════════════════════════════════
// TECHNICAL INDICATOR CALCULATIONS - IDENTICAL TO STRATEGY
// ═══════════════════════════════════════════════════════════════════════════════

// Moving Averages - Optimized for Indian market volatility
ema_fast_line = ta.ema(close, ema_fast)
ema_slow_line = ta.ema(close, ema_slow)
ema_trend_up = ema_fast_line > ema_slow_line
ema_trend_strength = math.abs(ema_fast_line - ema_slow_line) / close * 100

// RSI Momentum - Tuned for intraday scalping
rsi = ta.rsi(close, rsi_period)
rsi_bullish = rsi > 50 and rsi < rsi_overbought
rsi_bearish = rsi < 50 and rsi > rsi_oversold
rsi_extreme_ob = rsi > rsi_overbought
rsi_extreme_os = rsi < rsi_oversold

// Volume Analysis - Critical for Indian markets
volume_ma = ta.sma(volume, 20)
volume_spike = volume > volume_ma * volume_multiplier
volume_above_avg = volume > volume_ma

// ATR Volatility - For signal filtering
atr = ta.atr(atr_period)
atr_ma = ta.sma(atr, atr_period)
high_volatility = atr > atr_ma * 1.5
normal_volatility = atr <= atr_ma * 1.3

// Price Action - Indian market specific patterns
price_momentum_up = close > close[1] and close[1] > close[2]
price_momentum_down = close < close[1] and close[1] < close[2]

// Market Structure - Higher highs, lower lows (short-term for scalping)
higher_high = high > ta.highest(high[1], 3)
lower_low = low < ta.lowest(low[1], 3)
market_structure_bullish = higher_high and ema_trend_up
market_structure_bearish = lower_low and not ema_trend_up

// ═══════════════════════════════════════════════════════════════════════════════
// SIGNAL GENERATION LOGIC - IDENTICAL TO STRATEGY
// ═══════════════════════════════════════════════════════════════════════════════

// Signal timing control
var int last_signal_bar = 0
bars_since_signal = bar_index - last_signal_bar
sufficient_gap = bars_since_signal >= min_candle_gap

// Primary Buy Conditions - Aggressive but filtered
buy_condition_1 = ema_trend_up and ema_trend_strength > 0.05  // Strong uptrend
buy_condition_2 = rsi_bullish or rsi_extreme_os  // RSI momentum or oversold bounce
buy_condition_3 = price_momentum_up  // Price momentum confirmation
buy_condition_4 = volume_filter ? volume_above_avg : true  // Volume confirmation
buy_condition_5 = market_structure_bullish  // Market structure support

// Primary Sell Conditions - Aggressive but filtered
sell_condition_1 = not ema_trend_up and ema_trend_strength > 0.05  // Strong downtrend
sell_condition_2 = rsi_bearish or rsi_extreme_ob  // RSI momentum or overbought rejection
sell_condition_3 = price_momentum_down  // Price momentum confirmation
sell_condition_4 = volume_filter ? volume_above_avg : true  // Volume confirmation
sell_condition_5 = market_structure_bearish  // Market structure resistance

// Volatility and session filters
volatility_ok = volatility_filter ? normal_volatility : true
session_ok = is_market_open and not is_market_opening and not is_square_off_time

// Final signal generation - Require 4 out of 5 conditions + filters
buy_score = (buy_condition_1 ? 1 : 0) + (buy_condition_2 ? 1 : 0) + (buy_condition_3 ? 1 : 0) +
            (buy_condition_4 ? 1 : 0) + (buy_condition_5 ? 1 : 0)
sell_score = (sell_condition_1 ? 1 : 0) + (sell_condition_2 ? 1 : 0) + (sell_condition_3 ? 1 : 0) +
             (sell_condition_4 ? 1 : 0) + (sell_condition_5 ? 1 : 0)

buy_signal = buy_score >= 4 and sufficient_gap and volatility_ok and session_ok
sell_signal = sell_score >= 4 and sufficient_gap and volatility_ok and session_ok

// Update signal timing
if buy_signal or sell_signal
    last_signal_bar := bar_index

// Signal strength calculation
signal_strength = buy_signal ? buy_score : sell_signal ? -sell_score : 0
overall_signal = buy_signal ? "BUY" : sell_signal ? "SELL" : "HOLD"

// ═══════════════════════════════════════════════════════════════════════════════
// VISUALIZATION AND PLOTTING
// ═══════════════════════════════════════════════════════════════════════════════

// EMA Lines - Show trend direction
plot(show_ema_lines ? ema_fast_line : na, "Fast EMA", color=color.blue, linewidth=2)
plot(show_ema_lines ? ema_slow_line : na, "Slow EMA", color=color.red, linewidth=2)

// Signal size mapping
signal_size_value = signal_size == "Small" ? size.small : signal_size == "Large" ? size.large : size.normal

// Buy Signals - Green triangles below bars
plotshape(show_signals and buy_signal, "Buy Signal", shape.triangleup, location.belowbar,
          color.new(color.green, 0), size=signal_size_value,
          text=show_signal_labels ? "BUY" : "")

// Sell Signals - Red triangles above bars
plotshape(show_signals and sell_signal, "Sell Signal", shape.triangledown, location.abovebar,
          color.new(color.red, 0), size=signal_size_value,
          text=show_signal_labels ? "SELL" : "")

// Hold Signal - Gray dots (only when no buy/sell signal)
hold_signal = not buy_signal and not sell_signal and session_ok
plotshape(show_signals and hold_signal and barstate.islast, "Hold Signal", shape.circle, location.absolute,
          color.new(color.gray, 50), size=size.tiny,
          text=show_signal_labels ? "HOLD" : "")

// Signal Strength Histogram (optional)
plot(show_strength_histogram ? signal_strength : na, "Signal Strength",
     color=signal_strength > 0 ? color.green : signal_strength < 0 ? color.red : color.gray,
     style=plot.style_histogram, display=display.data_window)

// Market Session Background
bgcolor(highlight_session and is_market_open ? color.new(color.blue, 95) :
        highlight_session and not is_market_open ? color.new(color.gray, 98) : na,
        title="Market Session")

// Trend Background
bgcolor(ema_trend_up and ema_trend_strength > 0.1 ? color.new(color.green, 97) :
        not ema_trend_up and ema_trend_strength > 0.1 ? color.new(color.red, 97) : na,
        title="Trend Background")

// ═══════════════════════════════════════════════════════════════════════════════
// SIGNAL CONFIDENCE AND QUALITY INDICATORS
// ═══════════════════════════════════════════════════════════════════════════════

// Signal confidence levels
high_confidence = (buy_signal and buy_score == 5) or (sell_signal and sell_score == 5)
medium_confidence = (buy_signal and buy_score == 4) or (sell_signal and sell_score == 4)

// Quality indicators for signals
trend_aligned = (buy_signal and ema_trend_up) or (sell_signal and not ema_trend_up)
volume_confirmed = volume_above_avg
momentum_confirmed = (buy_signal and rsi_bullish) or (sell_signal and rsi_bearish)

// Signal quality score (0-3)
signal_quality = (trend_aligned ? 1 : 0) + (volume_confirmed ? 1 : 0) + (momentum_confirmed ? 1 : 0)

// High quality signal markers (all 3 quality factors present)
high_quality_buy = buy_signal and signal_quality == 3
high_quality_sell = sell_signal and signal_quality == 3

// Mark high quality signals with stars
plotshape(high_quality_buy, "High Quality Buy", shape.labelup, location.belowbar,
          color.new(color.lime, 0), size=size.small, text="⭐")
plotshape(high_quality_sell, "High Quality Sell", shape.labeldown, location.abovebar,
          color.new(color.orange, 0), size=size.small, text="⭐")

// ═══════════════════════════════════════════════════════════════════════════════
// INFORMATION TABLE - COMPACT VERSION
// ═══════════════════════════════════════════════════════════════════════════════

// Create compact information table
var table signal_table = table.new(position.top_left, 2, 8, bgcolor=color.white, border_width=1)

if barstate.islast
    // Header
    table.cell(signal_table, 0, 0, "🇮🇳 SCALPING SIGNALS", text_color=color.white, bgcolor=color.navy, text_size=size.small)
    table.cell(signal_table, 1, 0, "STATUS", text_color=color.white, bgcolor=color.navy, text_size=size.small)

    // Current Signal
    signal_color = overall_signal == "BUY" ? color.green : overall_signal == "SELL" ? color.red : color.gray
    table.cell(signal_table, 0, 1, "Signal", text_color=color.black, text_size=size.tiny)
    table.cell(signal_table, 1, 1, overall_signal, text_color=color.white, bgcolor=signal_color, text_size=size.tiny)

    // Signal Strength
    table.cell(signal_table, 0, 2, "Strength", text_color=color.black, text_size=size.tiny)
    table.cell(signal_table, 1, 2, str.tostring(math.abs(signal_strength)) + "/5",
               text_color=math.abs(signal_strength) >= 4 ? color.green : color.orange, text_size=size.tiny)

    // Market Status
    table.cell(signal_table, 0, 3, "Market", text_color=color.black, text_size=size.tiny)
    table.cell(signal_table, 1, 3, is_market_open ? "OPEN" : "CLOSED",
               text_color=is_market_open ? color.green : color.red, text_size=size.tiny)

    // Trend
    table.cell(signal_table, 0, 4, "Trend", text_color=color.black, text_size=size.tiny)
    table.cell(signal_table, 1, 4, ema_trend_up ? "BULLISH" : "BEARISH",
               text_color=ema_trend_up ? color.green : color.red, text_size=size.tiny)

    // RSI
    table.cell(signal_table, 0, 5, "RSI", text_color=color.black, text_size=size.tiny)
    table.cell(signal_table, 1, 5, str.tostring(math.round(rsi, 1)),
               text_color=rsi > rsi_overbought ? color.red : rsi < rsi_oversold ? color.green : color.black, text_size=size.tiny)

    // Volume
    table.cell(signal_table, 0, 6, "Volume", text_color=color.black, text_size=size.tiny)
    table.cell(signal_table, 1, 6, volume_spike ? "HIGH" : volume_above_avg ? "ABOVE" : "NORMAL",
               text_color=volume_spike ? color.orange : volume_above_avg ? color.blue : color.gray, text_size=size.tiny)

    // Signal Quality
    table.cell(signal_table, 0, 7, "Quality", text_color=color.black, text_size=size.tiny)
    table.cell(signal_table, 1, 7, str.tostring(signal_quality) + "/3",
               text_color=signal_quality == 3 ? color.green : signal_quality >= 2 ? color.orange : color.red, text_size=size.tiny)

// ═══════════════════════════════════════════════════════════════════════════════
// CURRENT SIGNAL DISPLAY - LARGE LABEL
// ═══════════════════════════════════════════════════════════════════════════════

// Display current signal as large label on latest bar
if barstate.islast and show_signal_labels
    signal_text = overall_signal + "\n" + str.tostring(math.abs(signal_strength)) + "/5"
    signal_bg_color = overall_signal == "BUY" ? color.green : overall_signal == "SELL" ? color.red : color.gray

    label.new(bar_index, high + (high - low) * 0.15,
              text=signal_text,
              style=label.style_label_down,
              color=signal_bg_color,
              textcolor=color.white,
              size=size.large)

// ═══════════════════════════════════════════════════════════════════════════════
// ALERT CONDITIONS
// ═══════════════════════════════════════════════════════════════════════════════

// Buy Signal Alerts
if enable_buy_alerts
    alertcondition(buy_signal, title="🇮🇳 BUY SIGNAL",
                   message="🚀 INDIAN SCALPING BUY SIGNAL!\nSymbol: {{ticker}}\nPrice: {{close}}\nTime: {{time}}\nStrength: " + str.tostring(buy_score) + "/5")

// Sell Signal Alerts
if enable_sell_alerts
    alertcondition(sell_signal, title="🇮🇳 SELL SIGNAL",
                   message="🔻 INDIAN SCALPING SELL SIGNAL!\nSymbol: {{ticker}}\nPrice: {{close}}\nTime: {{time}}\nStrength: " + str.tostring(sell_score) + "/5")

// Hold Signal Alerts (when no clear direction)
if enable_hold_alerts
    alertcondition(hold_signal and barstate.islast, title="🇮🇳 HOLD SIGNAL",
                   message="⏸️ INDIAN SCALPING HOLD!\nSymbol: {{ticker}}\nPrice: {{close}}\nTime: {{time}}\nReason: No clear signal")

// High Quality Signal Alerts
alertcondition(high_quality_buy, title="⭐ HIGH QUALITY BUY",
               message="⭐ HIGH QUALITY BUY SIGNAL!\nSymbol: {{ticker}}\nPrice: {{close}}\nTime: {{time}}\nQuality: 3/3")

alertcondition(high_quality_sell, title="⭐ HIGH QUALITY SELL",
               message="⭐ HIGH QUALITY SELL SIGNAL!\nSymbol: {{ticker}}\nPrice: {{close}}\nTime: {{time}}\nQuality: 3/3")

// ═══════════════════════════════════════════════════════════════════════════════
// INTEGRATION FEATURES FOR AUTOMATED TRADING
// ═══════════════════════════════════════════════════════════════════════════════

// JSON formatted alerts for Angel One SmartAPI integration
buy_alert_json = '{"action":"BUY","symbol":"{{ticker}}","price":{{close}},"time":"{{time}}","strategy":"Indian_Scalping_Indicator","strength":' + str.tostring(buy_score) + ',"quality":' + str.tostring(signal_quality) + ',"session":"' + (is_market_open ? "OPEN" : "CLOSED") + '"}'

sell_alert_json = '{"action":"SELL","symbol":"{{ticker}}","price":{{close}},"time":"{{time}}","strategy":"Indian_Scalping_Indicator","strength":' + str.tostring(sell_score) + ',"quality":' + str.tostring(signal_quality) + ',"session":"' + (is_market_open ? "OPEN" : "CLOSED") + '"}'

// Webhook-ready alerts for external systems
alertcondition(buy_signal, title="🔗 WEBHOOK BUY", message=buy_alert_json)
alertcondition(sell_signal, title="🔗 WEBHOOK SELL", message=sell_alert_json)

// ═══════════════════════════════════════════════════════════════════════════════
// PERFORMANCE MONITORING FEATURES
// ═══════════════════════════════════════════════════════════════════════════════

// Signal frequency tracking
var int total_signals_generated = 0
var int buy_signals_count = 0
var int sell_signals_count = 0

if buy_signal
    total_signals_generated += 1
    buy_signals_count += 1

if sell_signal
    total_signals_generated += 1
    sell_signals_count += 1

// Signal distribution
buy_signal_percentage = total_signals_generated > 0 ? (buy_signals_count / total_signals_generated) * 100 : 0
sell_signal_percentage = total_signals_generated > 0 ? (sell_signals_count / total_signals_generated) * 100 : 0

// Display signal statistics on chart (optional debug info)
if barstate.islast and total_signals_generated > 10  // Only show after sufficient signals
    debug_label_text = "Signals: " + str.tostring(total_signals_generated) +
                      "\nBuy: " + str.tostring(math.round(buy_signal_percentage, 1)) + "%" +
                      "\nSell: " + str.tostring(math.round(sell_signal_percentage, 1)) + "%"

    // Uncomment the line below to show debug statistics
    // label.new(bar_index - 20, low - (high - low) * 0.1, text=debug_label_text, style=label.style_label_up, color=color.new(color.blue, 80), textcolor=color.white, size=size.tiny)

// ═══════════════════════════════════════════════════════════════════════════════
// INDICATOR DOCUMENTATION AND USAGE GUIDE
// ═══════════════════════════════════════════════════════════════════════════════

// INDICATOR OVERVIEW:
// This Pine Script indicator is the visual companion to the Indian Intraday Scalping Strategy
// and provides clear buy/sell/hold signals for manual trading and automation support.
//
// KEY FEATURES:
// 1. VISUAL SIGNALS: Clear buy (green triangles) and sell (red triangles) markers
// 2. SIGNAL QUALITY: Star markers for high-quality signals (3/3 quality score)
// 3. REAL-TIME UPDATES: Signals update in real-time during market hours
// 4. MARKET AWARENESS: Respects Indian market hours (9:15 AM - 3:30 PM IST)
// 5. INTEGRATION READY: JSON alerts compatible with Angel One SmartAPI
//
// SIGNAL INTERPRETATION:
// - GREEN TRIANGLE (BUY): Strong bullish signal with 4+ conditions met
// - RED TRIANGLE (SELL): Strong bearish signal with 4+ conditions met
// - STAR MARKER: High-quality signal with trend, volume, and momentum alignment
// - GRAY DOT (HOLD): No clear directional signal, wait for better opportunity
//
// SIGNAL STRENGTH (1-5):
// - 5/5: All conditions met (strongest signal)
// - 4/5: Four conditions met (good signal)
// - 3/5 or below: Signal not generated (insufficient conditions)
//
// SIGNAL QUALITY (0-3):
// - 3/3: Trend aligned + Volume confirmed + Momentum confirmed (best quality)
// - 2/3: Two quality factors present (good quality)
// - 1/3: One quality factor present (moderate quality)
// - 0/3: No quality factors (avoid trading)
//
// USAGE RECOMMENDATIONS:
// 1. TIMEFRAMES: Best on 5m, 15m, 1H charts for intraday scalping
// 2. MARKETS: Optimized for Nifty 50, Sensex, and Indian large-cap stocks
// 3. TRADING STYLE: Quick scalping (hold positions for minutes to hours)
// 4. RISK MANAGEMENT: Always use stop-loss (1.5-2%) and take-profit (3-4%)
// 5. MARKET CONDITIONS: Works best in trending markets with good volume
//
// INTEGRATION WITH STRATEGY:
// - Use this indicator for visual confirmation of strategy signals
// - Both indicator and strategy use identical logic for consistency
// - Alerts from this indicator can trigger automated trading systems
// - Manual traders can use this for entry/exit timing
//
// AUTOMATION COMPATIBILITY:
// - Angel One SmartAPI: JSON alerts ready for webhook integration
// - TradingView Alerts: Set up alerts for buy/sell signals
// - Third-party Systems: Use webhook URLs to receive signal notifications
// - Risk Management: Always implement position sizing and stop-losses
//
// MARKET COMPLIANCE:
// - SEBI Compliant: Designed for individual retail traders
// - Intraday Focus: Automatic square-off awareness before market close
// - Indian Markets: Optimized for NSE/BSE trading characteristics
// - Session Management: Respects market hours and avoids pre-market noise

// Validation plot (required by TradingView)
plot(na, title="Indicator Validator")