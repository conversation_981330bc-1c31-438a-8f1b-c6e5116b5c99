//@version=6
strategy("Enhanced Indian Scalping Strategy", shorttitle="EISS", overlay=true,
         default_qty_type=strategy.percent_of_equity, default_qty_value=50,
         initial_capital=100000, currency=currency.INR, commission_type=strategy.commission.percent,
         commission_value=0.03, slippage=1, margin_long=100, margin_short=100,
         calc_on_every_tick=true, calc_on_order_fills=true)

// ═══════════════════════════════════════════════════════════════════════════════
// ENHANCED INDIAN INTRADAY SCALPING STRATEGY - ADVANCED MULTI-FACTOR SYSTEM
// Target: High-frequency scalping with institutional-grade risk management
// Market: Indian stock exchanges (NSE/BSE) - Retail trader optimized
// Trading Hours: 9:15 AM to 3:30 PM IST with lunch break awareness
// Capital Allocation: Dynamic volatility-based position sizing
// ═══════════════════════════════════════════════════════════════════════════════

// STRATEGY CONFIGURATION
enable_trading = input.bool(true, "🇮🇳 Enable Trading", tooltip="Enable/disable strategy execution")
trading_session = input.session("0915-1530", "📅 Trading Session (IST)", tooltip="Indian market hours: 9:15 AM to 3:30 PM IST")
lunch_break = input.session("1200-1300", "🍽️ Lunch Break (IST)", tooltip="Avoid trading during lunch break: 12:00-1:00 PM IST")
max_positions = input.int(1, "📊 Max Concurrent Positions", minval=1, maxval=3, tooltip="Maximum number of open positions")
base_position_size = input.float(50.0, "💰 Base Position Size (%)", minval=10.0, maxval=100.0, step=5.0, tooltip="Base percentage of equity per trade")

// ADVANCED RISK MANAGEMENT
use_dynamic_stops = input.bool(true, "🎯 Dynamic Stop Loss", tooltip="Use ATR-based dynamic stop loss")
atr_stop_multiplier = input.float(2.0, "📏 ATR Stop Multiplier", minval=1.0, maxval=5.0, step=0.1, tooltip="ATR multiplier for stop loss")
use_trailing_stop = input.bool(true, "🔄 Trailing Stop", tooltip="Enable trailing stop loss")
trailing_atr_mult = input.float(1.5, "📈 Trailing ATR Mult", minval=1.0, maxval=3.0, step=0.1, tooltip="ATR multiplier for trailing stop")
max_daily_loss = input.float(5.0, "🛡️ Max Daily Loss (%)", minval=1.0, maxval=10.0, step=0.5, tooltip="Maximum daily loss percentage")
use_position_sizing = input.bool(true, "📊 Volatility Position Sizing", tooltip="Adjust position size based on volatility")

// FALLBACK SETTINGS (for non-dynamic mode)
stop_loss_pct = input.float(1.5, "📉 Stop Loss (%)", minval=0.5, maxval=5.0, step=0.1, tooltip="Fixed stop loss percentage")
take_profit_pct = input.float(3.0, "📈 Take Profit (%)", minval=1.0, maxval=10.0, step=0.5, tooltip="Fixed take profit percentage")

// EXECUTION AND SLIPPAGE MODELING
model_slippage = input.bool(true, "⚡ Model Slippage", tooltip="Include realistic slippage modeling")
slippage_basis_points = input.float(3.0, "📊 Slippage (bps)", minval=1.0, maxval=10.0, step=0.5, tooltip="Slippage in basis points for Indian markets")
model_spread = input.bool(true, "📈 Model Bid-Ask Spread", tooltip="Include bid-ask spread impact")
spread_basis_points = input.float(2.0, "📊 Spread (bps)", minval=0.5, maxval=5.0, step=0.5, tooltip="Bid-ask spread in basis points")
max_position_value = input.float(500000, "💰 Max Position Value (₹)", minval=100000, maxval=2000000, step=50000, tooltip="Maximum position value to avoid liquidity issues")

// LIQUIDITY AND MARKET IMPACT
use_liquidity_filter = input.bool(true, "🌊 Liquidity Filter", tooltip="Filter trades based on liquidity")
min_volume_threshold = input.float(1000000, "📊 Min Volume (₹)", minval=500000, maxval=5000000, step=100000, tooltip="Minimum daily volume in rupees")
max_market_impact = input.float(0.1, "📈 Max Market Impact (%)", minval=0.05, maxval=0.5, step=0.05, tooltip="Maximum acceptable market impact")

// NEWS AND EVENT FILTERING
avoid_earnings = input.bool(true, "📰 Avoid Earnings", tooltip="Avoid trading around earnings announcements")
avoid_expiry = input.bool(true, "📅 Avoid F&O Expiry", tooltip="Avoid trading on F&O expiry days")
avoid_holidays = input.bool(true, "🏖️ Avoid Holidays", tooltip="Avoid trading before/after holidays")

// BROKER SPECIFIC CONSTRAINTS (ANGEL ONE)
angel_one_mode = input.bool(true, "👼 Angel One Mode", tooltip="Enable Angel One specific constraints")
max_orders_per_minute = input.int(10, "⚡ Max Orders/Min", minval=5, maxval=20, tooltip="Maximum orders per minute (API rate limit)")
min_order_value = input.float(500, "💰 Min Order Value (₹)", minval=100, maxval=1000, step=50, tooltip="Minimum order value for Angel One")

// TECHNICAL ANALYSIS SETTINGS
htf_timeframe = input.timeframe("15", "📈 Higher Timeframe", tooltip="Higher timeframe for trend confirmation")
macd_fast = input.int(12, "⚡ MACD Fast", minval=8, maxval=16, tooltip="MACD fast period")
macd_slow = input.int(26, "🐌 MACD Slow", minval=20, maxval=35, tooltip="MACD slow period")
macd_signal = input.int(9, "📡 MACD Signal", minval=7, maxval=12, tooltip="MACD signal period")
volume_lookback = input.int(50, "📊 Volume Lookback", minval=20, maxval=100, tooltip="Volume analysis lookback period")
pivot_lookback = input.int(20, "📍 Pivot Lookback", minval=10, maxval=50, tooltip="Pivot point calculation period")
gap_threshold = input.float(0.5, "🕳️ Gap Threshold (%)", minval=0.1, maxval=2.0, step=0.1, tooltip="Minimum gap percentage to consider")

// SCALPING FILTERS
min_candle_gap = input.int(3, "⏱️ Min Candle Gap", minval=1, maxval=10, tooltip="Minimum candles between signals")
volatility_regime = input.string("Adaptive", "🌊 Volatility Regime", options=["Low", "Medium", "High", "Adaptive"], tooltip="Volatility regime for filtering")
time_of_day_filter = input.bool(true, "🕐 Time of Day Filter", tooltip="Filter signals based on time of day")

// DRAWDOWN PROTECTION
use_equity_curve = input.bool(true, "📈 Equity Curve Filter", tooltip="Use equity curve analysis for drawdown protection")
drawdown_threshold = input.float(3.0, "📉 Drawdown Threshold (%)", minval=1.0, maxval=10.0, step=0.5, tooltip="Drawdown threshold for trade reduction")

// INTRADAY MANAGEMENT
auto_square_off = input.bool(true, "🔄 Auto Square-off", tooltip="Automatically close positions before market close")
square_off_time = input.string("1525", "⏰ Square-off Time", tooltip="Time to close all positions (HHMM format, IST)")
avoid_first_hour = input.bool(true, "🌅 Avoid First Hour", tooltip="Avoid trading in first hour (high volatility)")
avoid_last_hour = input.bool(true, "🌆 Avoid Last Hour", tooltip="Avoid new positions in last hour")

// ═══════════════════════════════════════════════════════════════════════════════
// ENHANCED MARKET HOURS AND SESSION MANAGEMENT
// ═══════════════════════════════════════════════════════════════════════════════

// Indian market session detection with lunch break
in_session = time(timeframe.period, trading_session, "Asia/Kolkata")
in_lunch_break = time(timeframe.period, lunch_break, "Asia/Kolkata")
is_market_open = not na(in_session) and na(in_lunch_break)

// Enhanced time calculations
current_time_ist = time("1", "0000-2359", "Asia/Kolkata")
square_off_timestamp = timestamp("Asia/Kolkata", year, month, dayofmonth, int(str.tonumber(str.substring(square_off_time, 0, 2))), int(str.tonumber(str.substring(square_off_time, 2, 4))))
is_square_off_time = current_time_ist >= square_off_timestamp

// Market session periods
market_open_time = timestamp("Asia/Kolkata", year, month, dayofmonth, 9, 15)
first_hour_end = timestamp("Asia/Kolkata", year, month, dayofmonth, 10, 15)
lunch_start = timestamp("Asia/Kolkata", year, month, dayofmonth, 12, 0)
lunch_end = timestamp("Asia/Kolkata", year, month, dayofmonth, 13, 0)
last_hour_start = timestamp("Asia/Kolkata", year, month, dayofmonth, 14, 30)
market_close_time = timestamp("Asia/Kolkata", year, month, dayofmonth, 15, 30)

// Session classifications
is_first_hour = avoid_first_hour and current_time_ist >= market_open_time and current_time_ist <= first_hour_end
is_lunch_time = current_time_ist >= lunch_start and current_time_ist <= lunch_end
is_last_hour = avoid_last_hour and current_time_ist >= last_hour_start and current_time_ist <= market_close_time
is_power_hour = current_time_ist >= timestamp("Asia/Kolkata", year, month, dayofmonth, 14, 0) and current_time_ist <= last_hour_start

// Optimal trading windows
morning_session = current_time_ist >= first_hour_end and current_time_ist < lunch_start
afternoon_session = current_time_ist >= lunch_end and current_time_ist < last_hour_start
is_optimal_time = morning_session or (afternoon_session and is_power_hour)

// Gap analysis for Indian markets
prev_close = close[1]
gap_size = math.abs(open - prev_close) / prev_close * 100
is_gap_up = open > prev_close * 1.002
is_gap_down = open < prev_close * 0.998
significant_gap = gap_size >= gap_threshold
gap_type = is_gap_up ? "GAP_UP" : is_gap_down ? "GAP_DOWN" : "NO_GAP"
gap_filter_ok = not significant_gap or gap_size < 2.0

// ═══════════════════════════════════════════════════════════════════════════════
// ENHANCED TECHNICAL INDICATOR CALCULATIONS
// ═══════════════════════════════════════════════════════════════════════════════

// Multi-timeframe trend analysis
htf_close = request.security(syminfo.tickerid, htf_timeframe, close)
htf_ema_fast = request.security(syminfo.tickerid, htf_timeframe, ta.ema(close, 21))
htf_ema_slow = request.security(syminfo.tickerid, htf_timeframe, ta.ema(close, 50))
htf_trend_up = htf_ema_fast > htf_ema_slow
htf_trend_strength = math.abs(htf_ema_fast - htf_ema_slow) / htf_close * 100

// Enhanced moving averages with multiple timeframes (look-ahead bias free)
ema_fast_line = ta.ema(close, 8)  // Faster for scalping
ema_slow_line = ta.ema(close, 21)  // More reliable
ema_trend_up = ema_fast_line[1] > ema_slow_line[1]
ema_trend_strength = math.abs(ema_fast_line[1] - ema_slow_line[1]) / close[1] * 100

// Additional trend confirmation
sma_200 = ta.sma(close, 200)  // Long-term trend
price_above_200sma = close[1] > sma_200[1]

// Enhanced RSI with divergence detection
rsi = ta.rsi(close, 14)  // Standard period for better signals
rsi_ma = ta.sma(rsi, 3)  // Smoothed RSI
rsi_bullish = rsi > 55 and rsi < 75  // Tighter range
rsi_bearish = rsi < 45 and rsi > 25  // Tighter range
rsi_extreme_ob = rsi > 80
rsi_extreme_os = rsi < 20

// RSI divergence detection (look-ahead bias free)
rsi_higher_high = rsi[1] > rsi[2] and rsi[2] > rsi[3]
rsi_lower_low = rsi[1] < rsi[2] and rsi[2] < rsi[3]
price_higher_high = high[1] > high[2] and high[2] > high[3]
price_lower_low = low[1] < low[2] and low[2] < low[3]
bullish_divergence = barstate.isconfirmed and price_lower_low and rsi_higher_high
bearish_divergence = barstate.isconfirmed and price_higher_high and rsi_lower_low

// MACD analysis (look-ahead bias free)
[macd_line, signal_line, histogram] = ta.macd(close, macd_fast, macd_slow, macd_signal)
macd_bullish = macd_line[1] > signal_line[1] and histogram[1] > histogram[2]
macd_bearish = macd_line[1] < signal_line[1] and histogram[1] < histogram[2]
macd_zero_cross_up = macd_line[1] > 0 and macd_line[2] <= 0
macd_zero_cross_down = macd_line[1] < 0 and macd_line[2] >= 0

// VWAP analysis for institutional flow
vwap_value = ta.vwap(hlc3)
price_above_vwap = close > vwap_value
vwap_distance = (close - vwap_value) / vwap_value * 100

// Enhanced volume analysis (look-ahead bias free)
volume_sma = ta.sma(volume, volume_lookback)
volume_ema = ta.ema(volume, 20)
volume_spike = volume[1] > volume_sma[1] * 2.0  // Stronger spike detection
volume_above_avg = volume[1] > volume_ema[1]
volume_declining = volume[1] < volume_sma[1] * 0.7

// Volume profile approximation
var float high_volume_price = na
var float low_volume_price = na
if volume == ta.highest(volume, 20)
    high_volume_price := hlc3

// ATR-based volatility analysis
atr = ta.atr(14)
atr_sma = ta.sma(atr, 20)
atr_percentile = ta.percentrank(atr, 50)

// Volatility regime classification
volatility_low = atr_percentile < 25
volatility_medium = atr_percentile >= 25 and atr_percentile < 75
volatility_high = atr_percentile >= 75

current_volatility_regime = volatility_regime == "Adaptive" ? (volatility_low ? "Low" : volatility_medium ? "Medium" : "High") : volatility_regime

// Enhanced price action patterns
price_momentum_strong_up = close[1] > close[2] and close[2] > close[3] and close[3] > close[4]
price_momentum_strong_down = close[1] < close[2] and close[2] < close[3] and close[3] < close[4]
inside_bar = high <= high[1] and low >= low[1]
outside_bar = high > high[1] and low < low[1]
doji = math.abs(close - open) <= (high - low) * 0.1

// Enhanced market structure with longer lookback
structure_lookback = 10
higher_high = high > ta.highest(high[1], structure_lookback)
lower_low = low < ta.lowest(low[1], structure_lookback)
market_structure_bullish = higher_high and ema_trend_up and htf_trend_up
market_structure_bearish = lower_low and not ema_trend_up and not htf_trend_up

// Pivot points for support/resistance
pivot_high = ta.pivothigh(high, pivot_lookback, pivot_lookback)
pivot_low = ta.pivotlow(low, pivot_lookback, pivot_lookback)
var float resistance_level = na
var float support_level = na

if not na(pivot_high)
    resistance_level := pivot_high
if not na(pivot_low)
    support_level := pivot_low

near_resistance = not na(resistance_level) and close >= resistance_level * 0.995 and close <= resistance_level * 1.005
near_support = not na(support_level) and close >= support_level * 0.995 and close <= support_level * 1.005

// ═══════════════════════════════════════════════════════════════════════════════
// EXECUTION AND LIQUIDITY ANALYSIS
// ═══════════════════════════════════════════════════════════════════════════════

// Liquidity analysis
daily_volume_value = volume * close  // Approximate daily volume in rupees
liquidity_adequate = use_liquidity_filter ? daily_volume_value > min_volume_threshold : true

// Market impact estimation
estimated_position_value = (strategy.equity * base_position_size / 100)
market_impact_estimate = estimated_position_value / daily_volume_value * 100
market_impact_acceptable = market_impact_estimate <= max_market_impact

// Slippage and spread modeling
slippage_cost = model_slippage ? close * (slippage_basis_points / 10000) : 0
spread_cost = model_spread ? close * (spread_basis_points / 10000) : 0
total_execution_cost = slippage_cost + spread_cost

// Order size constraints
max_affordable_qty = max_position_value / close
position_value_check = estimated_position_value <= max_position_value
min_order_check = estimated_position_value >= min_order_value

// API rate limiting simulation
var int orders_this_minute = 0
var int last_minute_bar = 0
current_minute = math.floor(time / 60000)

if current_minute != last_minute_bar
    orders_this_minute := 0
    last_minute_bar := current_minute

rate_limit_ok = angel_one_mode ? orders_this_minute < max_orders_per_minute : true

// News and event filtering
// F&O Expiry detection (last Thursday of month)
is_expiry_week = dayofweek == 5 and dayofmonth >= 22  // Approximate expiry detection
avoid_expiry_trading = avoid_expiry and is_expiry_week

// Holiday detection (simplified - major Indian holidays)
is_diwali_week = month == 11 and dayofmonth >= 1 and dayofmonth <= 7  // Approximate
is_holi_week = month == 3 and dayofmonth >= 8 and dayofmonth <= 15   // Approximate
avoid_holiday_trading = avoid_holidays and (is_diwali_week or is_holi_week)

// Earnings season detection (quarterly - simplified)
is_earnings_season = (month == 1 and dayofmonth <= 31) or (month == 4 and dayofmonth <= 30) or (month == 7 and dayofmonth <= 31) or (month == 10 and dayofmonth <= 31)
avoid_earnings_trading = avoid_earnings and is_earnings_season

// Combined execution filters
execution_filters_ok = liquidity_adequate and market_impact_acceptable and position_value_check and min_order_check and rate_limit_ok and not avoid_expiry_trading and not avoid_holiday_trading and not avoid_earnings_trading

// ═══════════════════════════════════════════════════════════════════════════════
// ENHANCED SIGNAL GENERATION LOGIC - MULTI-FACTOR ANALYSIS
// ═══════════════════════════════════════════════════════════════════════════════

// Equity curve analysis for drawdown protection
var float equity_peak = 0.0
var float current_drawdown = 0.0
var bool in_drawdown = false

if strategy.equity > equity_peak
    equity_peak := strategy.equity
current_drawdown := (equity_peak - strategy.equity) / equity_peak * 100
in_drawdown := current_drawdown > drawdown_threshold

// Daily loss tracking
var float daily_start_equity = 0.0
var float daily_loss = 0.0
if dayofweek != dayofweek[1] or barstate.isfirst
    daily_start_equity := strategy.equity
daily_loss := (daily_start_equity - strategy.equity) / daily_start_equity * 100
max_daily_loss_hit = daily_loss >= max_daily_loss

// Signal timing control with enhanced gap
var int last_signal_bar = 0
bars_since_signal = bar_index - last_signal_bar
signal_gap_ok = bars_since_signal >= min_candle_gap

// ═══════════════════════════════════════════════════════════════════════════════
// COMPREHENSIVE 15-POINT SCORING SYSTEM
// ═══════════════════════════════════════════════════════════════════════════════

// 1. TREND ANALYSIS (9 points - 30% weight)
trend_score_ema = ema_trend_up ? 2.0 : 0.0
trend_score_htf = htf_trend_up ? 2.0 : 0.0
trend_score_200sma = price_above_200sma ? 1.5 : 0.0
trend_score_strength = ema_trend_strength > 0.5 ? 1.5 : ema_trend_strength > 0.2 ? 1.0 : 0.0
trend_score_alignment = (ema_trend_up and htf_trend_up and price_above_200sma) ? 2.0 : 0.0
buy_trend_score = trend_score_ema + trend_score_htf + trend_score_200sma + trend_score_strength + trend_score_alignment
sell_trend_score = (not ema_trend_up ? 2.0 : 0.0) + (not htf_trend_up ? 2.0 : 0.0) + (not price_above_200sma ? 1.5 : 0.0) + trend_score_strength + ((not ema_trend_up and not htf_trend_up and not price_above_200sma) ? 2.0 : 0.0)

// 2. MOMENTUM ANALYSIS (7.5 points - 25% weight)
momentum_score_rsi = rsi_bullish ? 2.0 : rsi_bearish ? 0.0 : 1.0
momentum_score_macd = macd_bullish ? 2.0 : macd_bearish ? 0.0 : 1.0
momentum_score_divergence = bullish_divergence ? 2.0 : bearish_divergence ? 0.0 : 1.0
momentum_score_price = price_momentum_strong_up ? 1.5 : price_momentum_strong_down ? 0.0 : 0.75
buy_momentum_score = momentum_score_rsi + momentum_score_macd + momentum_score_divergence + momentum_score_price
sell_momentum_score = (rsi_bearish ? 2.0 : rsi_bullish ? 0.0 : 1.0) + (macd_bearish ? 2.0 : macd_bullish ? 0.0 : 1.0) + (bearish_divergence ? 2.0 : bullish_divergence ? 0.0 : 1.0) + (price_momentum_strong_down ? 1.5 : price_momentum_strong_up ? 0.0 : 0.75)

// 3. VOLUME ANALYSIS (6 points - 20% weight)
volume_score_spike = volume_spike ? 2.0 : volume_above_avg ? 1.0 : 0.0
volume_score_vwap = price_above_vwap ? 2.0 : 0.0
volume_score_profile = not na(high_volume_price) and close >= high_volume_price * 0.99 ? 2.0 : 0.0
buy_volume_score = volume_score_spike + volume_score_vwap + volume_score_profile
sell_volume_score = volume_score_spike + (not price_above_vwap ? 2.0 : 0.0) + (not na(high_volume_price) and close <= high_volume_price * 1.01 ? 2.0 : 0.0)

// 4. MARKET STRUCTURE (4.5 points - 15% weight)
structure_score_pivot = near_support ? 2.0 : near_resistance ? 0.0 : 1.0
structure_score_pattern = market_structure_bullish ? 1.5 : market_structure_bearish ? 0.0 : 0.75
structure_score_bars = outside_bar ? 1.0 : inside_bar ? 0.5 : doji ? 0.25 : 0.75
buy_structure_score = structure_score_pivot + structure_score_pattern + structure_score_bars
sell_structure_score = (near_resistance ? 2.0 : near_support ? 0.0 : 1.0) + (market_structure_bearish ? 1.5 : market_structure_bullish ? 0.0 : 0.75) + structure_score_bars

// 5. RISK ASSESSMENT (3 points - 10% weight)
risk_score_volatility = current_volatility_regime == "Low" ? 2.0 : current_volatility_regime == "Medium" ? 1.5 : 1.0
risk_score_gap = significant_gap ? 0.0 : 1.0
buy_risk_score = risk_score_volatility + risk_score_gap
sell_risk_score = risk_score_volatility + risk_score_gap

// TOTAL SCORES (OUT OF 15)
buy_total_score = buy_trend_score + buy_momentum_score + buy_volume_score + buy_structure_score + buy_risk_score
sell_total_score = sell_trend_score + sell_momentum_score + sell_volume_score + sell_structure_score + sell_risk_score

// Session and time filters
session_ok = is_market_open and not is_first_hour and not is_last_hour and not is_lunch_time
time_ok = time_of_day_filter ? is_optimal_time : true
equity_ok = use_equity_curve ? not in_drawdown : true
daily_loss_ok = not max_daily_loss_hit

// Final signal generation - Require high scores + all filters + execution constraints (look-ahead bias free)
min_score_threshold = 10.0  // Out of 15 (66.7% threshold)
buy_signal = enable_trading and buy_total_score >= min_score_threshold and signal_gap_ok and session_ok and time_ok and equity_ok and daily_loss_ok and execution_filters_ok and barstate.isconfirmed
sell_signal = enable_trading and sell_total_score >= min_score_threshold and signal_gap_ok and session_ok and time_ok and equity_ok and daily_loss_ok and execution_filters_ok and barstate.isconfirmed

// Update signal timing
if buy_signal or sell_signal
    last_signal_bar := bar_index

// ═══════════════════════════════════════════════════════════════════════════════
// ENHANCED POSITION MANAGEMENT AND RISK CONTROL
// ═══════════════════════════════════════════════════════════════════════════════

// Enhanced position tracking variables
var float entry_price = na
var float initial_stop_loss = na
var float current_stop_loss = na
var float take_profit_price = na
var float trailing_stop_price = na
var bool in_long_position = false
var bool in_short_position = false
var float position_atr = na
var float max_favorable_excursion = na

// Dynamic position sizing based on volatility and equity curve
volatility_adjustment = current_volatility_regime == "Low" ? 1.2 : current_volatility_regime == "Medium" ? 1.0 : 0.8
equity_curve_adjustment = use_equity_curve and in_drawdown ? 0.5 : 1.0
gap_adjustment = significant_gap ? 0.7 : 1.0

adjusted_position_size = base_position_size * volatility_adjustment * equity_curve_adjustment * gap_adjustment
position_size_capped = math.min(adjusted_position_size, 100.0)

// Calculate position quantity with enhanced sizing and constraints
risk_per_trade = use_position_sizing ? position_size_capped : base_position_size
base_qty = math.round((strategy.equity * risk_per_trade / 100) / close)

// Apply liquidity and value constraints
max_qty_by_value = math.floor(max_position_value / close)
max_qty_by_liquidity = math.floor(daily_volume_value * max_market_impact / 100 / close)

// Final position quantity with all constraints
position_qty = math.min(base_qty, math.min(max_qty_by_value, max_qty_by_liquidity))

// Ensure minimum order value is met
min_qty_required = math.ceil(min_order_value / close)
position_qty := math.max(position_qty, min_qty_required)

// Entry conditions with enhanced filters
can_enter_long = strategy.position_size == 0 and strategy.opentrades < max_positions and not max_daily_loss_hit
can_enter_short = strategy.position_size == 0 and strategy.opentrades < max_positions and not max_daily_loss_hit

// Enhanced Long Entry with dynamic stops and execution modeling
if buy_signal and can_enter_long
    // Calculate realistic entry price with execution costs
    execution_price = close + total_execution_cost  // Account for slippage and spread
    entry_price := execution_price
    position_atr := atr

    // Dynamic stop loss calculation with execution costs
    if use_dynamic_stops
        atr_stop_distance = atr * atr_stop_multiplier
        initial_stop_loss := execution_price - atr_stop_distance - total_execution_cost  // Account for exit costs
        current_stop_loss := initial_stop_loss
    else
        initial_stop_loss := execution_price * (1 - stop_loss_pct / 100) - total_execution_cost
        current_stop_loss := initial_stop_loss

    // Dynamic take profit based on volatility with execution costs
    if use_dynamic_stops
        take_profit_distance = atr * atr_stop_multiplier * 2.0  // 2:1 risk-reward
        take_profit_price := execution_price + take_profit_distance - total_execution_cost  // Account for exit costs
    else
        take_profit_price := execution_price * (1 + take_profit_pct / 100) - total_execution_cost

    // Initialize trailing stop
    if use_trailing_stop
        trailing_stop_price := execution_price - (atr * trailing_atr_mult)

    max_favorable_excursion := 0.0

    // Update order tracking
    orders_this_minute += 1

    strategy.entry("LONG", strategy.long, qty=position_qty, comment="🚀 LONG " + str.tostring(math.round(buy_total_score, 1)) + " Q:" + str.tostring(position_qty))
    in_long_position := true
    in_short_position := false

// Enhanced Short Entry with dynamic stops and execution modeling
if sell_signal and can_enter_short
    // Calculate realistic entry price with execution costs
    execution_price = close - total_execution_cost  // Account for slippage and spread (worse fill for short)
    entry_price := execution_price
    position_atr := atr

    // Dynamic stop loss calculation with execution costs
    if use_dynamic_stops
        atr_stop_distance = atr * atr_stop_multiplier
        initial_stop_loss := execution_price + atr_stop_distance + total_execution_cost  // Account for exit costs
        current_stop_loss := initial_stop_loss
    else
        initial_stop_loss := execution_price * (1 + stop_loss_pct / 100) + total_execution_cost
        current_stop_loss := initial_stop_loss

    // Dynamic take profit based on volatility with execution costs
    if use_dynamic_stops
        take_profit_distance = atr * atr_stop_multiplier * 2.0  // 2:1 risk-reward
        take_profit_price := execution_price - take_profit_distance + total_execution_cost  // Account for exit costs
    else
        take_profit_price := execution_price * (1 - take_profit_pct / 100) + total_execution_cost

    // Initialize trailing stop
    if use_trailing_stop
        trailing_stop_price := execution_price + (atr * trailing_atr_mult)

    max_favorable_excursion := 0.0

    // Update order tracking
    orders_this_minute += 1

    strategy.entry("SHORT", strategy.short, qty=position_qty, comment="🔻 SHORT " + str.tostring(math.round(sell_total_score, 1)) + " Q:" + str.tostring(position_qty))
    in_short_position := true
    in_long_position := false

// Enhanced trailing stop logic
if in_long_position and use_trailing_stop
    current_profit = close - entry_price
    if current_profit > max_favorable_excursion
        max_favorable_excursion := current_profit
        new_trailing_stop = close - (atr * trailing_atr_mult)
        if new_trailing_stop > trailing_stop_price
            trailing_stop_price := new_trailing_stop
            current_stop_loss := trailing_stop_price

if in_short_position and use_trailing_stop
    current_profit = entry_price - close
    if current_profit > max_favorable_excursion
        max_favorable_excursion := current_profit
        new_trailing_stop = close + (atr * trailing_atr_mult)
        if new_trailing_stop < trailing_stop_price
            trailing_stop_price := new_trailing_stop
            current_stop_loss := trailing_stop_price

// Enhanced Exit Conditions
long_stop_hit = in_long_position and close <= current_stop_loss
long_target_hit = in_long_position and close >= take_profit_price
short_stop_hit = in_short_position and close >= current_stop_loss
short_target_hit = in_short_position and close <= take_profit_price

// Signal-based exits with confirmation
long_exit_signal = in_long_position and sell_signal and sell_total_score >= min_score_threshold
short_exit_signal = in_short_position and buy_signal and buy_total_score >= min_score_threshold

// Time-based exits
square_off_exit = auto_square_off and is_square_off_time and (in_long_position or in_short_position)
emergency_exit = max_daily_loss_hit and (in_long_position or in_short_position)

// Execute enhanced exits with detailed comments
if long_stop_hit
    exit_reason = use_trailing_stop and current_stop_loss > initial_stop_loss ? "TRAIL SL" : "STOP LOSS"
    strategy.close("LONG", comment="🛡️ " + exit_reason)
    in_long_position := false

if long_target_hit
    strategy.close("LONG", comment="🎯 TARGET")
    in_long_position := false

if short_stop_hit
    exit_reason = use_trailing_stop and current_stop_loss < initial_stop_loss ? "TRAIL SL" : "STOP LOSS"
    strategy.close("SHORT", comment="🛡️ " + exit_reason)
    in_short_position := false

if short_target_hit
    strategy.close("SHORT", comment="🎯 TARGET")
    in_short_position := false

if long_exit_signal
    strategy.close("LONG", comment="🔄 SIGNAL EXIT")
    in_long_position := false

if short_exit_signal
    strategy.close("SHORT", comment="🔄 SIGNAL EXIT")
    in_short_position := false

if square_off_exit
    strategy.close_all(comment="⏰ SQUARE-OFF")
    in_long_position := false
    in_short_position := false

if emergency_exit
    strategy.close_all(comment="🚨 DAILY LOSS LIMIT")
    in_long_position := false
    in_short_position := false

// ═══════════════════════════════════════════════════════════════════════════════
// PERFORMANCE TRACKING
// ═══════════════════════════════════════════════════════════════════════════════

// Signal quality tracking
var int high_quality_signals = 0
var int medium_quality_signals = 0
var int low_quality_signals = 0

if buy_signal or sell_signal
    signal_score = buy_signal ? buy_total_score : sell_total_score
    if signal_score >= 13
        high_quality_signals += 1
    else if signal_score >= 11
        medium_quality_signals += 1
    else
        low_quality_signals += 1

// ═══════════════════════════════════════════════════════════════════════════════
// ENHANCED VISUALIZATION AND PLOTTING
// ═══════════════════════════════════════════════════════════════════════════════

// Enhanced EMA Lines with multi-timeframe
plot(ema_fast_line, "Fast EMA", color=color.blue, linewidth=2)
plot(ema_slow_line, "Slow EMA", color=color.red, linewidth=2)
plot(sma_200, "SMA 200", color=color.gray, linewidth=1)

// VWAP line
plot(use_vwap ? vwap_value : na, "VWAP", color=color.orange, linewidth=2)

// Support and resistance levels
plot(use_pivot_points and not na(resistance_level) ? resistance_level : na, "Resistance", color=color.red, style=plot.style_linebr, linewidth=2)
plot(use_pivot_points and not na(support_level) ? support_level : na, "Support", color=color.green, style=plot.style_linebr, linewidth=2)

// Enhanced entry signals with score display
plotshape(buy_signal, "Buy Signal", shape.triangleup, location.belowbar, color.new(color.lime, 0), size=size.normal, text="BUY")
plotshape(sell_signal, "Sell Signal", shape.triangledown, location.abovebar, color.new(color.red, 0), size=size.normal, text="SELL")

// High quality signal markers
plotshape(buy_signal and buy_total_score >= 13, "High Quality Buy", shape.labelup, location.belowbar, color.new(color.yellow, 0), size=size.small, text="⭐")
plotshape(sell_signal and sell_total_score >= 13, "High Quality Sell", shape.labeldown, location.abovebar, color.new(color.yellow, 0), size=size.small, text="⭐")

// Enhanced Stop Loss and Take Profit levels
plot(in_long_position and not na(current_stop_loss) ? current_stop_loss : na, "Long SL", color=color.new(color.red, 0), style=plot.style_linebr, linewidth=2)
plot(in_long_position and not na(take_profit_price) ? take_profit_price : na, "Long TP", color=color.new(color.green, 0), style=plot.style_linebr, linewidth=2)
plot(in_short_position and not na(current_stop_loss) ? current_stop_loss : na, "Short SL", color=color.new(color.red, 0), style=plot.style_linebr, linewidth=2)
plot(in_short_position and not na(take_profit_price) ? take_profit_price : na, "Short TP", color=color.new(color.green, 0), style=plot.style_linebr, linewidth=2)

// Trailing stop visualization
plot(in_long_position and use_trailing_stop and not na(trailing_stop_price) ? trailing_stop_price : na, "Trailing Stop", color=color.new(color.purple, 0), style=plot.style_circles, linewidth=1)

// Enhanced background for market sessions
bgcolor(is_market_open and not is_lunch_time ? color.new(color.blue, 95) : is_lunch_time ? color.new(color.yellow, 95) : color.new(color.gray, 98), title="Market Session")

// Background for volatility regime
bgcolor(current_volatility_regime == "High" ? color.new(color.red, 98) : current_volatility_regime == "Low" ? color.new(color.green, 98) : na, title="Volatility Regime")

// Background for trend with multi-timeframe confirmation
bgcolor(ema_trend_up and htf_trend_up and ema_trend_strength > 0.1 ? color.new(color.green, 97) : not ema_trend_up and not htf_trend_up and ema_trend_strength > 0.1 ? color.new(color.red, 97) : na, title="Confirmed Trend")

// ═══════════════════════════════════════════════════════════════════════════════
// ENHANCED INFORMATION DASHBOARD
// ═══════════════════════════════════════════════════════════════════════════════

// Create enhanced information table with execution metrics
var table info_table = table.new(position.top_right, 2, 25, bgcolor=color.white, border_width=1)

if barstate.islast
    // Header
    table.cell(info_table, 0, 0, "🇮🇳 INDIAN SCALPING STRATEGY", text_color=color.white, bgcolor=color.blue, text_size=size.small)
    table.cell(info_table, 1, 0, "STATUS", text_color=color.white, bgcolor=color.blue, text_size=size.small)

    // Market Status
    table.cell(info_table, 0, 1, "Market", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 1, is_market_open ? "OPEN" : "CLOSED",
               text_color=is_market_open ? color.green : color.red, text_size=size.tiny)

    // Current Position
    table.cell(info_table, 0, 2, "Position", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 2, in_long_position ? "LONG" : in_short_position ? "SHORT" : "NONE",
               text_color=in_long_position ? color.green : in_short_position ? color.red : color.gray, text_size=size.tiny)

    // Trend Analysis
    table.cell(info_table, 0, 3, "Trend", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 3, ema_trend_up ? "BULLISH" : "BEARISH",
               text_color=ema_trend_up ? color.green : color.red, text_size=size.tiny)

    // Trend Strength
    table.cell(info_table, 0, 4, "Strength", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 4, str.tostring(math.round(ema_trend_strength, 3)) + "%",
               text_color=ema_trend_strength > 0.1 ? color.blue : color.gray, text_size=size.tiny)

    // RSI
    table.cell(info_table, 0, 5, "RSI", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 5, str.tostring(math.round(rsi, 1)),
               text_color=rsi > 75 ? color.red : rsi < 25 ? color.green : color.black, text_size=size.tiny)

    // Volume
    table.cell(info_table, 0, 6, "Volume", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 6, volume_spike ? "HIGH" : volume_above_avg ? "ABOVE AVG" : "NORMAL",
               text_color=volume_spike ? color.orange : volume_above_avg ? color.blue : color.gray, text_size=size.tiny)

    // Volatility
    table.cell(info_table, 0, 7, "Volatility", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 7, volatility_high ? "HIGH" : "NORMAL",
               text_color=volatility_high ? color.red : color.green, text_size=size.tiny)

    // Entry Price
    table.cell(info_table, 0, 8, "Entry Price", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 8, not na(entry_price) ? str.tostring(entry_price, "#.##") : "N/A",
               text_color=color.black, text_size=size.tiny)

    // Stop Loss
    table.cell(info_table, 0, 9, "Stop Loss", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 9, not na(current_stop_loss) ? str.tostring(current_stop_loss, "#.##") : "N/A",
               text_color=color.red, text_size=size.tiny)

    // Take Profit
    table.cell(info_table, 0, 10, "Take Profit", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 10, not na(take_profit_price) ? str.tostring(take_profit_price, "#.##") : "N/A",
               text_color=color.green, text_size=size.tiny)

    // Performance Metrics
    table.cell(info_table, 0, 11, "Total Trades", text_color=color.black, bgcolor=color.yellow, text_size=size.tiny)
    table.cell(info_table, 1, 11, str.tostring(strategy.closedtrades), text_color=color.black, bgcolor=color.yellow, text_size=size.tiny)

    table.cell(info_table, 0, 12, "Win Rate", text_color=color.black, text_size=size.tiny)
    win_rate = strategy.closedtrades > 0 ? (strategy.wintrades / strategy.closedtrades) * 100 : 0
    table.cell(info_table, 1, 12, str.tostring(math.round(win_rate, 1)) + "%",
               text_color=win_rate >= 60 ? color.green : win_rate >= 40 ? color.orange : color.red, text_size=size.tiny)

    table.cell(info_table, 0, 13, "Total P&L", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 13, str.tostring(math.round(strategy.netprofit, 2)),
               text_color=strategy.netprofit >= 0 ? color.green : color.red, text_size=size.tiny)

    // Enhanced metrics
    table.cell(info_table, 0, 14, "HTF Trend", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 14, htf_trend_up ? "BULLISH" : "BEARISH",
               text_color=htf_trend_up ? color.green : color.red, text_size=size.tiny)

    table.cell(info_table, 0, 15, "Volatility Regime", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 15, current_volatility_regime,
               text_color=current_volatility_regime == "High" ? color.red : current_volatility_regime == "Low" ? color.green : color.orange, text_size=size.tiny)

    table.cell(info_table, 0, 16, "Daily Loss", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 16, str.tostring(math.round(daily_loss, 2)) + "%",
               text_color=daily_loss > 3 ? color.red : daily_loss > 1 ? color.orange : color.green, text_size=size.tiny)

    table.cell(info_table, 0, 17, "Drawdown", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 17, str.tostring(math.round(current_drawdown, 2)) + "%",
               text_color=current_drawdown > 5 ? color.red : current_drawdown > 2 ? color.orange : color.green, text_size=size.tiny)

    table.cell(info_table, 0, 18, "Position Size", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 18, str.tostring(math.round(risk_per_trade, 1)) + "%",
               text_color=color.blue, text_size=size.tiny)

    // Execution metrics
    table.cell(info_table, 0, 19, "Liquidity", text_color=color.black, text_size=size.tiny)
    liquidity_status = liquidity_adequate ? "ADEQUATE" : "LOW"
    table.cell(info_table, 1, 19, liquidity_status,
               text_color=liquidity_adequate ? color.green : color.red, text_size=size.tiny)

    table.cell(info_table, 0, 20, "Market Impact", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 20, str.tostring(math.round(market_impact_estimate, 3)) + "%",
               text_color=market_impact_acceptable ? color.green : color.red, text_size=size.tiny)

    table.cell(info_table, 0, 21, "Execution Cost", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 21, str.tostring(math.round(total_execution_cost, 2)),
               text_color=color.orange, text_size=size.tiny)

    table.cell(info_table, 0, 22, "Orders/Min", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 22, str.tostring(orders_this_minute) + "/" + str.tostring(max_orders_per_minute),
               text_color=rate_limit_ok ? color.green : color.red, text_size=size.tiny)

    table.cell(info_table, 0, 23, "Event Filter", text_color=color.black, text_size=size.tiny)
    event_status = avoid_expiry_trading or avoid_holiday_trading or avoid_earnings_trading ? "FILTERED" : "CLEAR"
    table.cell(info_table, 1, 23, event_status,
               text_color=event_status == "CLEAR" ? color.green : color.orange, text_size=size.tiny)

    // Current Signal with enhanced scoring
    current_signal = buy_total_score >= min_score_threshold ? "BUY" : sell_total_score >= min_score_threshold ? "SELL" : "HOLD"
    signal_score_display = buy_total_score >= min_score_threshold ? buy_total_score : sell_total_score >= min_score_threshold ? sell_total_score : 0
    signal_color = current_signal == "BUY" ? color.green : current_signal == "SELL" ? color.red : color.gray
    table.cell(info_table, 0, 24, "SIGNAL", text_color=color.white, bgcolor=color.navy, text_size=size.small)
    table.cell(info_table, 1, 24, current_signal + "\n" + str.tostring(math.round(signal_score_display, 1)) + "/15",
               text_color=color.white, bgcolor=signal_color, text_size=size.small)

// ═══════════════════════════════════════════════════════════════════════════════
// ALERTS AND NOTIFICATIONS
// ═══════════════════════════════════════════════════════════════════════════════

// Enhanced alert conditions (Strategy-compatible alerts using alert() function)
if buy_signal
    alert('{"action":"BUY","symbol":"' + syminfo.ticker + '","price":' + str.tostring(close) + ',"strategy":"Production_Indian_Scalping"}', alert.freq_once_per_bar)

if sell_signal
    alert('{"action":"SELL","symbol":"' + syminfo.ticker + '","price":' + str.tostring(close) + ',"strategy":"Production_Indian_Scalping"}', alert.freq_once_per_bar)

if buy_signal and buy_total_score >= 13
    alert('{"action":"HIGH_QUALITY_BUY","symbol":"' + syminfo.ticker + '","price":' + str.tostring(close) + ',"strategy":"Enhanced_Indian_Scalping","quality":"PREMIUM"}', alert.freq_once_per_bar)

if sell_signal and sell_total_score >= 13
    alert('{"action":"HIGH_QUALITY_SELL","symbol":"' + syminfo.ticker + '","price":' + str.tostring(close) + ',"strategy":"Enhanced_Indian_Scalping","quality":"PREMIUM"}', alert.freq_once_per_bar)

if long_stop_hit or short_stop_hit
    alert('{"action":"STOP_LOSS","symbol":"' + syminfo.ticker + '","price":' + str.tostring(close) + ',"strategy":"Enhanced_Indian_Scalping"}', alert.freq_once_per_bar)

if long_target_hit or short_target_hit
    alert('{"action":"TARGET","symbol":"' + syminfo.ticker + '","price":' + str.tostring(close) + ',"strategy":"Enhanced_Indian_Scalping"}', alert.freq_once_per_bar)

if square_off_exit
    alert('{"action":"SQUARE_OFF","symbol":"' + syminfo.ticker + '","price":' + str.tostring(close) + ',"strategy":"Enhanced_Indian_Scalping","reason":"MARKET_CLOSE"}', alert.freq_once_per_bar)

if emergency_exit
    alert('{"action":"EMERGENCY_EXIT","symbol":"' + syminfo.ticker + '","price":' + str.tostring(close) + ',"strategy":"Enhanced_Indian_Scalping","reason":"DAILY_LOSS_LIMIT"}', alert.freq_once_per_bar)

// ═══════════════════════════════════════════════════════════════════════════════
// STRATEGY DOCUMENTATION
// ═══════════════════════════════════════════════════════════════════════════════

// PRODUCTION-READY INDIAN SCALPING STRATEGY
// - 15-Point Scoring System (Trend 30%, Momentum 25%, Volume 20%, Structure 15%, Risk 10%)
// - Multi-timeframe Analysis (15m higher timeframe confirmation)
// - Dynamic ATR-based stops with trailing functionality
// - Volatility-based position sizing and gap analysis
// - Angel One SmartAPI compatible with execution modeling
// - Indian market hours (9:15-15:30 IST) with lunch break awareness
// - Event filtering (earnings, expiry, holidays)
// - Liquidity and market impact constraints
// - Real-time signal generation with comprehensive alerts

// Validation plot (required by TradingView)
plot(na, title="Clean Strategy Validator")