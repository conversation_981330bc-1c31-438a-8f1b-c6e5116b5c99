//@version=6
strategy("Indian Intraday Scalping Strategy", shorttitle="IISS", overlay=true,
         default_qty_type=strategy.percent_of_equity, default_qty_value=100,
         initial_capital=10000, currency=currency.INR, commission_type=strategy.commission.percent,
         commission_value=0.05, slippage=2, margin_long=100, margin_short=100)

// ═══════════════════════════════════════════════════════════════════════════════
// INDIAN INTRADAY SCALPING STRATEGY - OPTIMIZED FOR NIFTY 50 & SENSEX
// Target: Quick intraday sessions (minutes-based opportunities)
// Market: Indian stock exchanges (NSE/BSE) - Individual retail trader compliant
// Trading Hours: 9:15 AM to 3:30 PM IST
// Capital Allocation: Aggressive (100% equity utilization)
// ═══════════════════════════════════════════════════════════════════════════════

// STRATEGY CONFIGURATION
enable_trading = input.bool(true, "🇮🇳 Enable Trading", tooltip="Enable/disable strategy execution")
trading_session = input.session("0915-1530", "📅 Trading Session (IST)", tooltip="Indian market hours: 9:15 AM to 3:30 PM IST")
max_positions = input.int(1, "📊 Max Concurrent Positions", minval=1, maxval=3, tooltip="Maximum number of open positions")
position_size_pct = input.float(100.0, "💰 Position Size (%)", minval=10.0, maxval=100.0, step=5.0, tooltip="Percentage of equity per trade (aggressive = 100%)")

// RISK MANAGEMENT SETTINGS
use_stop_loss = input.bool(true, "🛡️ Enable Stop Loss", tooltip="Use stop loss for risk management")
stop_loss_pct = input.float(1.5, "📉 Stop Loss (%)", minval=0.5, maxval=5.0, step=0.1, tooltip="Stop loss percentage (tight for scalping)")
use_take_profit = input.bool(true, "🎯 Enable Take Profit", tooltip="Use take profit for profit booking")
take_profit_pct = input.float(3.0, "📈 Take Profit (%)", minval=1.0, maxval=10.0, step=0.1, tooltip="Take profit percentage (2:1 risk-reward)")
trailing_stop = input.bool(false, "🔄 Trailing Stop", tooltip="Enable trailing stop loss")

// TECHNICAL INDICATOR SETTINGS - OPTIMIZED FOR SCALPING
ema_fast = input.int(5, "⚡ Fast EMA", minval=3, maxval=10, tooltip="Fast EMA for trend detection")
ema_slow = input.int(13, "🐌 Slow EMA", minval=8, maxval=21, tooltip="Slow EMA for trend confirmation")
rsi_period = input.int(7, "📊 RSI Period", minval=5, maxval=14, tooltip="RSI period for momentum")
rsi_overbought = input.int(75, "📈 RSI Overbought", minval=70, maxval=85, tooltip="RSI overbought level")
rsi_oversold = input.int(25, "📉 RSI Oversold", minval=15, maxval=30, tooltip="RSI oversold level")
volume_multiplier = input.float(1.5, "📊 Volume Spike", minval=1.2, maxval=3.0, step=0.1, tooltip="Volume spike multiplier")
atr_period = input.int(14, "📏 ATR Period", minval=10, maxval=20, tooltip="ATR period for volatility")

// SCALPING FILTERS
min_candle_gap = input.int(2, "⏱️ Min Candle Gap", minval=1, maxval=5, tooltip="Minimum candles between signals")
volatility_filter = input.bool(true, "🌊 Volatility Filter", tooltip="Filter trades during high volatility")
volume_filter = input.bool(true, "📊 Volume Filter", tooltip="Require volume confirmation")

// INTRADAY SQUARE-OFF SETTINGS
auto_square_off = input.bool(true, "🔄 Auto Square-off", tooltip="Automatically close positions before market close")
square_off_time = input.string("1520", "⏰ Square-off Time", tooltip="Time to close all positions (HHMM format, IST)")

// ═══════════════════════════════════════════════════════════════════════════════
// MARKET HOURS AND SESSION MANAGEMENT
// ═══════════════════════════════════════════════════════════════════════════════

// Indian market session detection
in_session = time(timeframe.period, trading_session, "Asia/Kolkata")
is_market_open = not na(in_session)

// Square-off time calculation
current_time_ist = time("1", "0000-2359", "Asia/Kolkata")
square_off_timestamp = timestamp("Asia/Kolkata", year, month, dayofmonth,
                                int(str.tonumber(str.substring(square_off_time, 0, 2))),
                                int(str.tonumber(str.substring(square_off_time, 2, 4))))
is_square_off_time = current_time_ist >= square_off_timestamp

// Market opening detection (first 15 minutes)
market_open_time = timestamp("Asia/Kolkata", year, month, dayofmonth, 9, 15)
is_market_opening = current_time_ist >= market_open_time and current_time_ist <= market_open_time + 15 * 60 * 1000

// ═══════════════════════════════════════════════════════════════════════════════
// TECHNICAL INDICATOR CALCULATIONS
// ═══════════════════════════════════════════════════════════════════════════════

// Moving Averages - Optimized for Indian market volatility
ema_fast_line = ta.ema(close, ema_fast)
ema_slow_line = ta.ema(close, ema_slow)
ema_trend_up = ema_fast_line > ema_slow_line
ema_trend_strength = math.abs(ema_fast_line - ema_slow_line) / close * 100

// RSI Momentum - Tuned for intraday scalping
rsi = ta.rsi(close, rsi_period)
rsi_bullish = rsi > 50 and rsi < rsi_overbought
rsi_bearish = rsi < 50 and rsi > rsi_oversold
rsi_extreme_ob = rsi > rsi_overbought
rsi_extreme_os = rsi < rsi_oversold

// Volume Analysis - Critical for Indian markets
volume_ma = ta.sma(volume, 20)
volume_spike = volume > volume_ma * volume_multiplier
volume_above_avg = volume > volume_ma

// ATR Volatility - For position sizing and filters
atr = ta.atr(atr_period)
atr_ma = ta.sma(atr, atr_period)
high_volatility = atr > atr_ma * 1.5
normal_volatility = atr <= atr_ma * 1.3

// Price Action - Indian market specific patterns
price_momentum_up = close > close[1] and close[1] > close[2]
price_momentum_down = close < close[1] and close[1] < close[2]
inside_bar = high <= high[1] and low >= low[1]
outside_bar = high > high[1] and low < low[1]

// Market Structure - Higher highs, lower lows (short-term for scalping)
higher_high = high > ta.highest(high[1], 3)
lower_low = low < ta.lowest(low[1], 3)
market_structure_bullish = higher_high and ema_trend_up
market_structure_bearish = lower_low and not ema_trend_up

// ═══════════════════════════════════════════════════════════════════════════════
// SIGNAL GENERATION LOGIC - AGGRESSIVE SCALPING
// ═══════════════════════════════════════════════════════════════════════════════

// Signal timing control
var int last_signal_bar = 0
bars_since_signal = bar_index - last_signal_bar
sufficient_gap = bars_since_signal >= min_candle_gap

// Primary Buy Conditions - Aggressive but filtered
buy_condition_1 = ema_trend_up and ema_trend_strength > 0.05  // Strong uptrend
buy_condition_2 = rsi_bullish or rsi_extreme_os  // RSI momentum or oversold bounce
buy_condition_3 = price_momentum_up  // Price momentum confirmation
buy_condition_4 = volume_filter ? volume_above_avg : true  // Volume confirmation
buy_condition_5 = market_structure_bullish  // Market structure support

// Primary Sell Conditions - Aggressive but filtered
sell_condition_1 = not ema_trend_up and ema_trend_strength > 0.05  // Strong downtrend
sell_condition_2 = rsi_bearish or rsi_extreme_ob  // RSI momentum or overbought rejection
sell_condition_3 = price_momentum_down  // Price momentum confirmation
sell_condition_4 = volume_filter ? volume_above_avg : true  // Volume confirmation
sell_condition_5 = market_structure_bearish  // Market structure resistance

// Volatility and session filters
volatility_ok = volatility_filter ? normal_volatility : true
session_ok = is_market_open and not is_market_opening and not is_square_off_time

// Final signal generation - Require 4 out of 5 conditions + filters
buy_score = (buy_condition_1 ? 1 : 0) + (buy_condition_2 ? 1 : 0) + (buy_condition_3 ? 1 : 0) +
            (buy_condition_4 ? 1 : 0) + (buy_condition_5 ? 1 : 0)
sell_score = (sell_condition_1 ? 1 : 0) + (sell_condition_2 ? 1 : 0) + (sell_condition_3 ? 1 : 0) +
             (sell_condition_4 ? 1 : 0) + (sell_condition_5 ? 1 : 0)

buy_signal = enable_trading and buy_score >= 4 and sufficient_gap and volatility_ok and session_ok
sell_signal = enable_trading and sell_score >= 4 and sufficient_gap and volatility_ok and session_ok

// Update signal timing
if buy_signal or sell_signal
    last_signal_bar := bar_index

// ═══════════════════════════════════════════════════════════════════════════════
// POSITION MANAGEMENT AND RISK CONTROL
// ═══════════════════════════════════════════════════════════════════════════════

// Position tracking variables
var float entry_price = na
var float stop_loss_price = na
var float take_profit_price = na
var bool in_long_position = false
var bool in_short_position = false

// Calculate position size based on equity percentage
position_qty = math.round((strategy.equity * position_size_pct / 100) / close)

// Entry conditions with position limits
can_enter_long = strategy.position_size == 0 and strategy.opentrades < max_positions
can_enter_short = strategy.position_size == 0 and strategy.opentrades < max_positions

// Long Entry
if buy_signal and can_enter_long
    entry_price := close
    stop_loss_price := use_stop_loss ? close * (1 - stop_loss_pct / 100) : na
    take_profit_price := use_take_profit ? close * (1 + take_profit_pct / 100) : na

    strategy.entry("LONG", strategy.long, qty=position_qty, comment="🚀 LONG ENTRY")
    in_long_position := true
    in_short_position := false

// Short Entry (for individual traders - only if allowed by broker)
if sell_signal and can_enter_short
    entry_price := close
    stop_loss_price := use_stop_loss ? close * (1 + stop_loss_pct / 100) : na
    take_profit_price := use_take_profit ? close * (1 - take_profit_pct / 100) : na

    strategy.entry("SHORT", strategy.short, qty=position_qty, comment="🔻 SHORT ENTRY")
    in_short_position := true
    in_long_position := false

// Exit Conditions
long_stop_hit = in_long_position and use_stop_loss and close <= stop_loss_price
long_target_hit = in_long_position and use_take_profit and close >= take_profit_price
short_stop_hit = in_short_position and use_stop_loss and close >= stop_loss_price
short_target_hit = in_short_position and use_take_profit and close <= take_profit_price

// Opposite signal exits
long_exit_signal = in_long_position and sell_signal
short_exit_signal = in_short_position and buy_signal

// Auto square-off before market close
square_off_exit = auto_square_off and is_square_off_time and (in_long_position or in_short_position)

// Execute exits
if long_stop_hit
    strategy.close("LONG", comment="🛡️ LONG SL")
    in_long_position := false

if long_target_hit
    strategy.close("LONG", comment="🎯 LONG TP")
    in_long_position := false

if short_stop_hit
    strategy.close("SHORT", comment="🛡️ SHORT SL")
    in_short_position := false

if short_target_hit
    strategy.close("SHORT", comment="🎯 SHORT TP")
    in_short_position := false

if long_exit_signal
    strategy.close("LONG", comment="🔄 LONG EXIT")
    in_long_position := false

if short_exit_signal
    strategy.close("SHORT", comment="🔄 SHORT EXIT")
    in_short_position := false

if square_off_exit
    strategy.close_all(comment="⏰ SQUARE-OFF")
    in_long_position := false
    in_short_position := false

// ═══════════════════════════════════════════════════════════════════════════════
// PERFORMANCE TRACKING AND ANALYTICS
// ═══════════════════════════════════════════════════════════════════════════════

// Performance variables
var int total_trades_count = 0
var int winning_trades_count = 0
var float total_profit_points = 0.0
var float largest_win_points = 0.0
var float largest_loss_points = 0.0
var float win_rate_pct = 0.0

// Track trade completion
trade_completed = strategy.closedtrades > strategy.closedtrades[1]

if trade_completed
    last_trade_profit = strategy.closedtrades.profit(strategy.closedtrades - 1)
    last_trade_points = last_trade_profit / syminfo.mintick

    total_trades_count += 1
    total_profit_points += last_trade_points

    if last_trade_profit > 0
        winning_trades_count += 1
        if last_trade_points > largest_win_points
            largest_win_points := last_trade_points
    else
        if last_trade_points < largest_loss_points
            largest_loss_points := last_trade_points

    win_rate_pct := total_trades_count > 0 ? (winning_trades_count / total_trades_count) * 100 : 0

// ═══════════════════════════════════════════════════════════════════════════════
// VISUALIZATION AND PLOTTING
// ═══════════════════════════════════════════════════════════════════════════════

// EMA Lines
plot(ema_fast_line, "Fast EMA", color=color.blue, linewidth=2)
plot(ema_slow_line, "Slow EMA", color=color.red, linewidth=2)

// Entry signals
plotshape(buy_signal, "Buy Signal", shape.triangleup, location.belowbar,
          color.new(color.green, 0), size=size.normal, text="BUY")
plotshape(sell_signal, "Sell Signal", shape.triangledown, location.abovebar,
          color.new(color.red, 0), size=size.normal, text="SELL")

// Stop Loss and Take Profit levels
plot(in_long_position and not na(stop_loss_price) ? stop_loss_price : na,
     "Long SL", color=color.new(color.red, 0), style=plot.style_linebr, linewidth=1)
plot(in_long_position and not na(take_profit_price) ? take_profit_price : na,
     "Long TP", color=color.new(color.green, 0), style=plot.style_linebr, linewidth=1)
plot(in_short_position and not na(stop_loss_price) ? stop_loss_price : na,
     "Short SL", color=color.new(color.red, 0), style=plot.style_linebr, linewidth=1)
plot(in_short_position and not na(take_profit_price) ? take_profit_price : na,
     "Short TP", color=color.new(color.green, 0), style=plot.style_linebr, linewidth=1)

// Background for market session
bgcolor(is_market_open ? color.new(color.blue, 95) : color.new(color.gray, 98), title="Market Session")

// Background for trend
bgcolor(ema_trend_up and ema_trend_strength > 0.1 ? color.new(color.green, 97) :
        not ema_trend_up and ema_trend_strength > 0.1 ? color.new(color.red, 97) : na,
        title="Trend Background")

// ═══════════════════════════════════════════════════════════════════════════════
// INFORMATION DASHBOARD
// ═══════════════════════════════════════════════════════════════════════════════

// Create comprehensive information table
var table info_table = table.new(position.top_right, 2, 15, bgcolor=color.white, border_width=1)

if barstate.islast
    // Header
    table.cell(info_table, 0, 0, "🇮🇳 INDIAN SCALPING STRATEGY", text_color=color.white, bgcolor=color.blue, text_size=size.small)
    table.cell(info_table, 1, 0, "STATUS", text_color=color.white, bgcolor=color.blue, text_size=size.small)

    // Market Status
    table.cell(info_table, 0, 1, "Market", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 1, is_market_open ? "OPEN" : "CLOSED",
               text_color=is_market_open ? color.green : color.red, text_size=size.tiny)

    // Current Position
    table.cell(info_table, 0, 2, "Position", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 2, in_long_position ? "LONG" : in_short_position ? "SHORT" : "NONE",
               text_color=in_long_position ? color.green : in_short_position ? color.red : color.gray, text_size=size.tiny)

    // Trend Analysis
    table.cell(info_table, 0, 3, "Trend", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 3, ema_trend_up ? "BULLISH" : "BEARISH",
               text_color=ema_trend_up ? color.green : color.red, text_size=size.tiny)

    // Trend Strength
    table.cell(info_table, 0, 4, "Strength", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 4, str.tostring(math.round(ema_trend_strength, 3)) + "%",
               text_color=ema_trend_strength > 0.1 ? color.blue : color.gray, text_size=size.tiny)

    // RSI
    table.cell(info_table, 0, 5, "RSI", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 5, str.tostring(math.round(rsi, 1)),
               text_color=rsi > rsi_overbought ? color.red : rsi < rsi_oversold ? color.green : color.black, text_size=size.tiny)

    // Volume
    table.cell(info_table, 0, 6, "Volume", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 6, volume_spike ? "HIGH" : volume_above_avg ? "ABOVE AVG" : "NORMAL",
               text_color=volume_spike ? color.orange : volume_above_avg ? color.blue : color.gray, text_size=size.tiny)

    // Volatility
    table.cell(info_table, 0, 7, "Volatility", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 7, high_volatility ? "HIGH" : "NORMAL",
               text_color=high_volatility ? color.red : color.green, text_size=size.tiny)

    // Entry Price
    table.cell(info_table, 0, 8, "Entry Price", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 8, not na(entry_price) ? str.tostring(entry_price, "#.##") : "N/A",
               text_color=color.black, text_size=size.tiny)

    // Stop Loss
    table.cell(info_table, 0, 9, "Stop Loss", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 9, not na(stop_loss_price) ? str.tostring(stop_loss_price, "#.##") : "N/A",
               text_color=color.red, text_size=size.tiny)

    // Take Profit
    table.cell(info_table, 0, 10, "Take Profit", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 10, not na(take_profit_price) ? str.tostring(take_profit_price, "#.##") : "N/A",
               text_color=color.green, text_size=size.tiny)

    // Performance Metrics
    table.cell(info_table, 0, 11, "Total Trades", text_color=color.black, bgcolor=color.yellow, text_size=size.tiny)
    table.cell(info_table, 1, 11, str.tostring(total_trades_count), text_color=color.black, bgcolor=color.yellow, text_size=size.tiny)

    table.cell(info_table, 0, 12, "Win Rate", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 12, str.tostring(math.round(win_rate_pct, 1)) + "%",
               text_color=win_rate_pct >= 60 ? color.green : win_rate_pct >= 40 ? color.orange : color.red, text_size=size.tiny)

    table.cell(info_table, 0, 13, "Total P&L", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 13, str.tostring(math.round(strategy.netprofit, 2)),
               text_color=strategy.netprofit >= 0 ? color.green : color.red, text_size=size.tiny)

    // Current Signal
    current_signal = buy_score >= 4 ? "BUY" : sell_score >= 4 ? "SELL" : "HOLD"
    signal_color = current_signal == "BUY" ? color.green : current_signal == "SELL" ? color.red : color.gray
    table.cell(info_table, 0, 14, "SIGNAL", text_color=color.white, bgcolor=color.navy, text_size=size.small)
    table.cell(info_table, 1, 14, current_signal, text_color=color.white, bgcolor=signal_color, text_size=size.small)

// ═══════════════════════════════════════════════════════════════════════════════
// ALERTS AND NOTIFICATIONS
// ═══════════════════════════════════════════════════════════════════════════════

// Alert conditions for external integration (Angel One SmartAPI compatible)
alertcondition(buy_signal, title="🇮🇳 BUY SIGNAL",
               message='{"action":"BUY","symbol":"{{ticker}}","price":{{close}},"time":"{{time}}","strategy":"Indian_Scalping"}')

alertcondition(sell_signal, title="🇮🇳 SELL SIGNAL",
               message='{"action":"SELL","symbol":"{{ticker}}","price":{{close}},"time":"{{time}}","strategy":"Indian_Scalping"}')

alertcondition(long_stop_hit or short_stop_hit, title="🛡️ STOP LOSS HIT",
               message='{"action":"STOP_LOSS","symbol":"{{ticker}}","price":{{close}},"time":"{{time}}","strategy":"Indian_Scalping"}')

alertcondition(long_target_hit or short_target_hit, title="🎯 TARGET HIT",
               message='{"action":"TARGET","symbol":"{{ticker}}","price":{{close}},"time":"{{time}}","strategy":"Indian_Scalping"}')

alertcondition(square_off_exit, title="⏰ SQUARE-OFF",
               message='{"action":"SQUARE_OFF","symbol":"{{ticker}}","price":{{close}},"time":"{{time}}","strategy":"Indian_Scalping"}')

// ═══════════════════════════════════════════════════════════════════════════════
// STRATEGY DOCUMENTATION
// ═══════════════════════════════════════════════════════════════════════════════

// STRATEGY OVERVIEW:
// This Pine Script strategy is specifically designed for Indian intraday scalping
// targeting Nifty 50 and Sensex indices with the following characteristics:
//
// 1. MARKET FOCUS: Indian stock exchanges (NSE/BSE)
// 2. TRADING STYLE: Aggressive intraday scalping (minutes-based opportunities)
// 3. TRADER TYPE: Individual retail traders (SEBI compliant)
// 4. CAPITAL ALLOCATION: Aggressive (up to 100% equity utilization)
// 5. RISK MANAGEMENT: Tight stop losses (1.5%) with 2:1 risk-reward ratio
// 6. SESSION MANAGEMENT: Automatic square-off before market close
//
// TECHNICAL APPROACH:
// - Dual EMA system for trend identification
// - RSI for momentum confirmation
// - Volume analysis for signal validation
// - Market structure analysis for additional confirmation
// - Volatility filters to avoid choppy markets
//
// RISK CONTROLS:
// - Maximum position limits
// - Automatic stop-loss and take-profit
// - Market hours enforcement
// - Volatility-based trade filtering
// - Mandatory square-off before market close
//
// PERFORMANCE TRACKING:
// - Real-time win rate calculation
// - Trade count and P&L tracking
// - Performance metrics display
// - Alert system for external integration
//
// INTEGRATION READY:
// - Angel One SmartAPI compatible alerts
// - JSON formatted messages for automation
// - Real-time signal generation
// - No historical trade interference

// Validation plot (required by TradingView)
plot(na, title="Strategy Validator")